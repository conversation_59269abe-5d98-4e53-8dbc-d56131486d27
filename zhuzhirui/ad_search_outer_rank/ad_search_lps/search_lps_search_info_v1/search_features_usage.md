# 搜索特征增益建模使用说明

## 🐾 功能概述

已为 `kai_v2_model.py` 添加了搜索特征增益建模功能，实现：
1. 从 sparse embedding 中提取 37 个搜索相关特征
2. 使用类似 share_bottom 的网络结构处理搜索特征
3. 输出搜索 logits 与原始 ctcvr logits 相加，实现增益建模（bias 建模）

## 📋 搜索特征列表

### 用户搜索特征 (9个)
- `ExtractSearchQuerySource` (field=94) - 搜索查询来源
- `ExtractSearchReferPhotoId` (field=96) - 搜索参考照片ID
- `ExtractSearchQueryCategoryCalss3` (field=109) - 搜索查询三级类目
- `ExtractSearchQueryCategoryCalss2` (field=115) - 搜索查询二级类目
- `ExtractQuery` (field=116) - 查询词
- `ExtractQuerytoken` (field=117) - 查询词token
- `ExtractSearchFromPage` (field=118) - 搜索来源页面
- `ExtractSearchPosId` (field=119) - 搜索位置ID
- `ExtractSearchEnterSource` (field=127) - 搜索进入来源

### 照片搜索特征 (7个)
- `ExtractSearchRecallMatchtype` (field=199) - 搜索召回匹配类型
- `ExtractSearchRecallRelevance` (field=200) - 搜索召回相关性
- `ExtractSearchRecallStrategy` (field=201) - 搜索召回策略
- `ExtractSearchRecallStrategyType` (field=202) - 搜索召回策略类型
- `ExtractSearchRewriteQuery` (field=203) - 搜索查询重写
- `ExtractSearchQrScore` (field=204) - 搜索QR分数
- `ExtractSearchExtendType` (field=205) - 搜索扩展类型

### 组合搜索特征 (21个)
- `ExtractSearchKboxType` (field=213) - 搜索Kbox类型
- `ExtractSearchPhotoPname` (field=235) - 搜索照片产品名
- `ExtractSearchPhotoPname2` (field=236) - 搜索照片产品名2
- `ExtractSearchPhotoAsr` (field=237) - 搜索照片ASR
- `ExtractSearchPhotoAsr2` (field=238) - 搜索照片ASR2
- `ExtractSearchPhotoCname` (field=239) - 搜索照片类目名
- `ExtractSearchPhotoCname2` (field=240) - 搜索照片类目名2
- `ExtractSearchPhotoDescription` (field=241) - 搜索照片描述
- `ExtractSearchPhotoDescription2` (field=242) - 搜索照片描述2
- `ExtractSearchPhotoOcr` (field=243) - 搜索照片OCR
- `ExtractSearchPhotoOcr2` (field=244) - 搜索照片OCR2
- `ExtractSearchPhotoOcrTitle` (field=245) - 搜索照片OCR标题
- `ExtractSearchPhotoOcrTitle2` (field=246) - 搜索照片OCR标题2
- `ExtractSearchPhotoSlogan` (field=247) - 搜索照片标语
- `ExtractSearchPhotoSlogan2` (field=248) - 搜索照片标语2
- `ExtractSearchBidword` (field=251) - 搜索竞价词
- `ExtractSearchParserTextTokenV1` (field=254) - 搜索解析文本Token
- `ExtractSearchParserTextV1` (field=255) - 搜索解析文本
- `ExtractSearchQueryCombineMatchNum` (field=256) - 搜索查询组合匹配数

## 🔧 实现细节

### 1. 特征提取函数
```python
def extract_search_features(self, sparse_input):
    """从 sparse embedding 中提取搜索相关特征"""
    # 37个搜索特征，每个16维，总计592维
    # 返回 shape: [batch_size, 37 * 16 = 592]
```

### 2. 阶段1：搜索特征的 share_bottom 网络（先变大）
```python
# 搜索特征先通过 share_bottom 扩展表达能力：592 → 1024
for i in range(self._config.klearn_conf.share_num):  # share_num = 1
    with tf.variable_scope("search_share_bottom_layer_{}".format(i)):
        layer_size = self._dnn_net_size[i]
        search_input = self.fc(search_input, search_input_size, layer_size, i, is_train, is_cpu_ps)
        search_input_size = layer_size
```

### 3. 阶段2：搜索特征的 task head（小头缩小）
```python
# 搜索特征通过小头逐步缩小：1024 → 256 → 128 → 2
for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
    with tf.variable_scope("search_head_layer_{}".format(i)):
        layer_size = self._dnn_net_size[i]
        search_head_input = self.fc(search_head_input, search_head_input_size, layer_size, i, is_train, is_cpu_ps)
        search_head_input_size = layer_size

search_ctcvr_logits = search_head_input  # shape: [batch_size, 2]
```

### 4. 增益建模（Bias 建模）
```python
# 将搜索 logits 与原始 ctcvr logits 相加
enhanced_ctcvr_logits = tf.add(original_ctcvr_logits, search_ctcvr_logits, name="enhanced_ctcvr_logits")

# 使用增强后的 logits 计算概率和损失
prob = tf.nn.softmax(enhanced_ctcvr_logits, name="softmax")
cross_entropy = tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctcvr, logits=enhanced_ctcvr_logits)
```

## 🏗️ 网络架构对比

### 原始网络架构
```
输入特征 → share_bottom(1024) → task_head(256→128→2) → ctcvr_logits
```

### 搜索特征网络架构
```
搜索特征(592) → share_bottom(1024) → task_head(256→128→2) → search_logits
                                                                    ↓
原始ctcvr_logits + search_logits = enhanced_ctcvr_logits → 最终预测
```

## 📊 维度变化

| 阶段 | 维度 | 说明 |
|------|------|------|
| 原始 sparse_input | [batch, 259*16=4144] | 完整的稀疏特征 |
| 提取的 search_features | [batch, 37*16=592] | 搜索相关特征 |
| **阶段1：share_bottom** | | |
| search_share_bottom 输出 | [batch, 1024] | 搜索特征先变大，扩展表达能力 |
| **阶段2：task_head** | | |
| search_head layer_1 | [batch, 256] | 小头开始缩小 |
| search_head layer_2 | [batch, 128] | 继续缩小 |
| search_ctcvr_logits | [batch, 2] | 搜索特征的最终 logits |
| **增益建模** | | |
| original_ctcvr_logits | [batch, 2] | 原始 ctcvr logits |
| enhanced_ctcvr_logits | [batch, 2] | 增强后的 ctcvr logits |

## 🎯 使用效果

1. **增益建模**: 搜索特征作为对原始 ctcvr 预测的增益调整（bias 建模）
2. **特征专门化**: 搜索特征通过独立的 share_bottom 网络处理，学习搜索相关模式
3. **网络一致性**: 搜索网络结构与原始网络保持一致，确保训练稳定性
4. **模块化设计**: 搜索增益网络独立，便于调试和优化
5. **可解释性**: 可以单独分析搜索特征对最终预测的贡献度

## 🔍 调试建议

1. **查看日志**: 关注 `search_features shape` 和 `search_output shape` 的日志输出
2. **特征分析**: 可以在训练过程中输出搜索特征的统计信息
3. **网络调优**: 可以调整搜索网络的层数、维度和dropout率
4. **效果对比**: 可以通过A/B测试对比使用搜索网络前后的效果

## 🚀 扩展建议

1. **注意力机制**: 可以为搜索特征添加注意力权重
2. **分组处理**: 可以将用户/照片/组合搜索特征分别处理后融合
3. **残差连接**: 可以在搜索网络中添加残差连接
4. **特征选择**: 可以根据重要性动态选择搜索特征

## 📝 注意事项

1. 确保 `kai_feature.txt` 中的特征顺序与代码中的 field 编号一致
2. 搜索网络的参数需要与其他网络一起训练
3. 可以根据实际效果调整搜索网络的结构和参数
4. 建议在验证集上监控搜索特征的贡献度
